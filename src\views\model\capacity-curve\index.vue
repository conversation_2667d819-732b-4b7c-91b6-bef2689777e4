<template>
  <div class="common-table-page">
    <div class="container">
      <div class="content-left">
        <div class="query-form">
          <a-form-model
            style="padding: 0px 30px 0px 30px"
            :label-col="labelCol"
            ref="form"
            :model="form"
            :wrapper-col="wrapperCol"
          >
            <!-- allow-clear -->
            <a-form-model-item label="库容曲线">
              <a-select
                placeholder="请选择"
                v-model="form.curveId"
                @change="handleChange(form.curveId)"
                style="width: 400px"
                show-search
                :filter-option="filterOption"
              >
                <a-select-option v-for="(d, index) in reservoirList" :key="index" :value="d.curveId">
                  {{ d.curveName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-form-model>
          <div class="query-form-btns">
            <a-button class="btn" type="primary" icon="upload" @click="handleUpload" :loading="loading">上传</a-button>
            <a-button class="btn" type="primary" icon="download" @click="handleExport" :loading="exportLoading">
              下载
            </a-button>
            <a-button class="btn" :disabled="isDefault" @click="setDefault">设为默认</a-button>
          </div>
        </div>
        <!-- <div class="header">桃花江水库库容曲线</div> -->
        <div class="line-title">桃花江水库库容曲线</div>

        <LineTypeEchart
          :dataSource="pieConfig.dataSource"
          v-if="allList.length > 0"
          :custom="chartConfig.custom"
          key="1"
          height="360px"
        />
        <a-empty style="margin-top: 20px; flex: 1" v-else />
        <!-- height="380px" -->
        <div class="line-title">桃花江水库水文特征点</div>
        <VxeTable
          v-if="list?.length"
          ref="vxeTableRef"
          :isShowTableHeader="false"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :stripe="false"
          :tablePage="false"
        ></VxeTable>
        <a-empty style="margin-top: 30px; flex: 1" v-else />
      </div>
      <div class="content-right">
        <div class="query-form">
          <div>水位库容计算：</div>

          <a-radio-group v-model="form.waterIndex" class="radio-group">
            <a-radio value="1" class="group-item">
              <div class="title">水位(m)</div>
              <a-input-number
                class="value"
                :precision="2"
                allowClear
                :disabled="form.waterIndex !== '1'"
                v-model="form.waterLevel"
                placeholder="请输入"
              />
            </a-radio>
            <a-radio value="2" class="group-item">
              <div class="title">水面面积(万m²)</div>
              <a-input-number
                class="value"
                :precision="2"
                allowClear
                :disabled="form.waterIndex !== '2'"
                v-model="form.waterArea"
                placeholder="请输入"
              />
            </a-radio>
            <a-radio value="3" class="group-item">
              <div class="title">库容(百万m³)</div>
              <a-input-number
                class="value"
                :precision="4"
                allowClear
                :disabled="form.waterIndex !== '3'"
                v-model="form.storageCapacity"
                placeholder="请输入"
              />
            </a-radio>
          </a-radio-group>
          <div class="query-form-btns">
            <!--  -->
            <a-button
              class="btn"
              type="primary"
              :disabled="
                isConvert ||
                (form.waterIndex == '1' && !form.waterLevel) ||
                (form.waterIndex == '2' && !form.waterArea) ||
                (form.waterIndex == '3' && !form.storageCapacity)
              "
              @click="handleConvert"
              :loading="convertLoading"
            >
              计算
            </a-button>
            <a-button class="btn" @click="resetConvert">清空</a-button>
          </div>
        </div>
        <div class="pie-title">桃花江水库库容计算表</div>

        <!-- style="height: 660px" style="padding-bottom: 10px" :otherHeight="230"-->
        <VxeTable
          class="all-table"
          v-if="allList?.length"
          ref="vxeTableRef"
          :isShowTableHeader="false"
          :columns="columns"
          :tableData="allList"
          :loading="loading"
          :stripe="false"
          :tablePage="false"
        ></VxeTable>

        <a-empty style="margin-top: 30px; flex: 1" v-else />
      </div>
    </div>

    <FormDrawer
      v-if="showFormDrawer"
      :reservoirList="reservoirList"
      ref="formDrawerRef"
      @ok="onOperationComplete"
      @close="showFormDrawer = false"
    />
  </div>
</template>

<script lang="jsx">
  import { getProjectTree, getOptions, getPolderList } from '@/api/common'
  import { getReservoirCurveList, getReservoirCurveLoadById, convertReservoirCurve, setDefault } from './services'

  import moment from 'moment'

  import BaseEchart from '@/components/Echarts/BaseEchart.vue'
  import * as echarts from 'echarts/core'
  import { LineEchart, PieEchart } from '@/components/Echarts'
  import LineTypeEchart from './components/LineTypeEchart.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import excelExport from '@/utils/excelExport.js'
  import FormDrawer from './components/FormDrawer'

  export default {
    name: 'CapacityCurve',
    components: {
      LineEchart,
      LineTypeEchart,
      PieEchart,
      VxeTable,
      VxeTableForm,
      FormDrawer,
    },
    data() {
      return {
        showFormDrawer: false,
        exportLoading: false,
        convertLoading: false,
        isConvert: true,
        allList: [],
        list: [],
        total: 0,
        isDefault: false,
        reservoirList: [],

        schemeOptions: [],
        scheduleOptions: [],
        executionOptions: [],

        labelCol: { span: 4 },
        wrapperCol: { span: 16 },
        polderOptions: [],
        terminals: [],
        loading: false,
        pieConfig: {
          dataSource: [],
          custom: {
            legend: { show: false },
          },
        },
        chartConfig: {
          dataSource: [],
          custom: {
            yLabel: '单位',
            toolbox: {
              feature: {
                saveAsImage: {},
              },
            },
            shortValue: true,
            // legend: { left: 'center' },
            showAreaStyle: false,
            dataZoom: true,
          },
        },

        clickIndex: 0,
        indexInfo: {},
        indexKey: 0,

        projectOptions: [],
        form: {
          waterIndex: '1',
          // 水位
          waterLevel: '',
          // 水面积
          waterArea: '',
          // 库容
          storageCapacity: '',
          curveId: '',
          terminalId: '',
          indexCode: '',
          timeRange: null,
          projectCode: undefined,
          siteName: '',
          siteId: null,
          startTime: '',
          endTime: '',
          sort: [],
        },
        columns: [
          {
            title: '水文特征点',
            field: 'feature',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '水位(m)',
            field: 'wl',
            minWidth: 80,
          },
          {
            title: '水面面积(万㎡)',
            field: 'area',
            minWidth: 80,
          },
          {
            title: '库容(百万m³)',
            field: 'volume',
            minWidth: 80,
          },
          {
            title: '备注',
            field: 'remark',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
        ],
      }
    },
    created() {
      this.init()
    },
    mounted() {},
    methods: {
      init() {
        getReservoirCurveList().then(res => {
          this.reservoirList = res.data
          this.form.curveId = this.reservoirList[0]?.curveId
          this.getList(this.form.curveId)
        })
      },
      disabledDate(current) {
        // Can not select days before today and today
        return current && current > new Date()
      },
      //切换库容曲线
      handleChange(curveId) {
        this.resetConvert()

        this.getList(curveId)
      },
      //计算水位库容
      handleConvert() {
        let param = {
          curveId: this.form.curveId,
          type: this.form.waterIndex,
          value:
            this.form.waterIndex == 1
              ? this.form.waterLevel
              : this.form.waterIndex == 2
              ? this.form.waterArea
              : this.form.storageCapacity,
        }
        convertReservoirCurve(param).then(res => {
          this.form.waterLevel = res.data.wl
          this.form.waterArea = res.data.area
          this.form.storageCapacity = res.data.volume
        })
      },
      resetConvert() {
        this.form.waterLevel = null
        this.form.waterArea = null
        this.form.storageCapacity = null
      },
      handleExport() {
        this.exportLoading = true
        getReservoirCurveLoadById({ curveId: this.form.curveId }).then(res => {
          this.exportLoading = false

          const columnsList = [
            {
              title: '水文特征点',
              field: 'feature',
              minWidth: 120,
            },
            {
              title: '水位(m)',
              field: 'wl',
              minWidth: 80,
            },
            {
              title: '水面面积(万㎡)',
              field: 'area',
              minWidth: 80,
            },
            {
              title: '库容(百万m³)',
              field: 'volume',
              minWidth: 80,
            },
            {
              title: '备注',
              field: 'remark',
              minWidth: 100,
            },
          ]

          const data = res?.data

          excelExport(columnsList, data, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`)
        })
      },
      /** 查询列表 */
      getList(curveId) {
        this.loading = true
        let item = this.reservoirList.find(el => el.curveId == curveId)
        this.isDefault = item?.isDefault == 1 ? true : false
        this.isConvert = false
        getReservoirCurveLoadById({ curveId: curveId }).then(res => {
          this.loading = false
          this.list = res.data.filter(item => item.feature != '')
          this.allList = res.data
          this.pieConfig.dataSource = [
            {
              xData1: res.data?.map(el => el.area),
              xData2: res.data?.map(el => el.volume),
              sData1: res.data?.map(el => el.area),
              sData2: res.data?.map(el => el.volume),
              yData: res.data?.map(el => el.wl),
            },
          ]
        })
      },
      // 设置默认
      setDefault() {
        if (this.form.curveId == '') {
          this.$message.error(`请选择曲线`, 3)
          return
        }
        if (this.isDefault) {
          this.$message.error(`该曲线已是默认曲线`, 3)
          return
        }
        setDefault({ curveId: this.form.curveId }).then(res => {
          this.isDefault = true
          this.$message.success(`成功设置默认数据`, 3)
        })
      },

      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },

      /** 搜索按钮操作 */
      handleUpload() {
        this.showFormDrawer = true

        this.$nextTick(() => {
          this.$refs.formDrawerRef.handleAdd(this.form.curveId)
        })
      },
      // 操作完成后
      onOperationComplete() {
        this.init()
        // this.$refs.treeGeneralRef.getDataSource()
      },
    },
  }
</script>
<style lang="less" scoped>
  .container {
    width: 100%;
    height: 100%;
    display: flex;
    background: #fff;
    padding: 10px;
    // flex-direction: column;
    .content-left {
      width: 50%;
      height: 100%;
      .query-form {
        display: flex;
        .query-form-btns {
          .btn {
            margin-left: 10px;
          }
        }
      }
      .header {
        font-weight: 600;
      }
      .line-title {
        // margin: 10px 0 10px 210px;
        margin-bottom: 30px;
        width: 100%;
        // text-align: center;
        font-weight: 600;
        font-size: 16px;
      }
      .legend {
        // margin: 20px 0 0 160px;
        width: 100%;
        // width: 610px;
        justify-content: center;
        // height: 68px;
        // background: rgba(78, 153, 248, 0.2);
        display: flex;
        .legend-item {
          border: 1px solid #d9d9d9;
          padding: 5px 10px;
          cursor: pointer;
          // margin: 0 10px;
        }
        .legend-item.navActive {
          color: rgba(96, 171, 246, 1);
          // border: 2px solid;
          border: 1px solid rgba(96, 171, 246, 0.8);
        }
        .legend-item:hover {
          color: rgba(96, 171, 246, 1);
          // border: 2px solid;
          border: 1px solid rgba(96, 171, 246, 0.8);
        }
      }
      .chart {
        width: 100%;
        height: 90%;
        // height: 68px;
        // background: rgba(78, 153, 248, 0.2);
        display: flex;
        .legend-item {
          border: 1px solid #d9d9d9;
          padding: 5px 10px;
          cursor: pointer;
          // margin: 0 10px;
        }
        .legend-item.navActive {
          color: rgba(96, 171, 246, 1);
          // border: 2px solid;
          border: 1px solid rgba(96, 171, 246, 0.8);
        }
        .legend-item:hover {
          color: rgba(96, 171, 246, 1);
          // border: 2px solid;
          border: 1px solid rgba(96, 171, 246, 0.8);
        }
      }
    }
    .content-right {
      width: 50%;
      height: 100%;
      .query-form {
        display: flex;
        position: relative;
        .radio-group {
          display: flex;
          flex-direction: column;
          margin-left: 20px;
          .group-item {
            margin-bottom: 10px;
            height: 30px;
            display: flex;
            position: relative;
            flex-wrap: nowrap;
            .title {
              margin-right: 10px;
              width: 400px;
            }
            .value {
              position: absolute;
              top: 0px;
              right: 2px;
              width: 200px;
            }
          }
        }

        .query-form-btns {
          position: absolute;
          right: 10px;
          bottom: 10px;
          .btn {
            margin-left: 10px;
          }
        }
      }

      .pie-title {
        // margin: 10px 0 10px 210px;
        font-weight: 600;
        width: 100%;
        // text-align: center;
        font-size: 16px;
      }
      .all-table {
        margin-top: 10px;
        height: calc(100vh - 250px);
        padding-bottom: 10px;
        // background: red;
      }
    }
  }
</style>
