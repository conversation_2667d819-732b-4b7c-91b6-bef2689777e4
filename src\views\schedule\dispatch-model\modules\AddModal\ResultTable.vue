<template>
  <VxeTable
    ref="vxeTableRef"
    :columns="columns"
    :tableData="$attrs.dataSource"
    size="small"
    :tablePage="false"
    :isShowTableHeader="false"
  ></VxeTable>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable/index.vue'

  export default {
    name: 'ResultTable',
    props: ['resultData'],
    components: {
      VxeTable,
    },
    data() {
      return {
        columns: [
          {
            title: '时间',
            field: 'tm',
            minWidth: 140,
            fixed: 'left',
          },
          {
            title: '时段降雨量(mm)',
            field: 'rain',
            minWidth: 120,
            align: 'center',
          },
          {
            title: '水位(m)',
            field: 'wlv',
            minWidth: 100,
            align: 'center',
          },
          {
            title: '入库流量(m³/s)',
            field: 'inflow',
            minWidth: 120,
            align: 'center',
          },
          {
            title: '供水流量(m³/s)',
            field: 'supplyFlow',
            minWidth: 120,
            align: 'center',
          },
          {
            title: '泄洪流量(m³/s)',
            field: 'floodFlow',
            minWidth: 120,
            align: 'center',
          },
          {
            title: '供水量(万m³)',
            field: 'supplyVolume',
            minWidth: 110,
            align: 'center',
          },
          {
            title: '泄洪量(万m³)',
            field: 'floodVolume',
            minWidth: 110,
            align: 'center',
          },
        ],
      }
    },
    computed: {},
    created() {},
    methods: {},
  }
</script>

<style lang="less" scoped></style>
