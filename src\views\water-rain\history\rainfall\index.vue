<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <a-tabs v-model="treeTabKey" @change="key => (treeTabKey = key)">
        <a-tab-pane key="1" tab="按站点">
          <TreeGeneral
            v-if="treeTabKey === '1' && !!categoryTreeOptions.dataSource.length"
            :key="1"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :currentKeys="currentKeys"
            :treeOptions="categoryTreeOptions"
            @onTreeMounted="onTreeMounted"
            @check="(keys, nodes) => clickTreeNode(keys, nodes)"
          />
        </a-tab-pane>
        <a-tab-pane key="2" tab="按区域">
          <TreeGeneral
            v-if="treeTabKey === '2' && !!districtTreeOptions.dataSource.length"
            :key="2"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :currentKeys="currentKeys"
            :treeOptions="districtTreeOptions"
            @onTreeMounted="onTreeMounted"
            @check="(keys, nodes) => clickTreeNode(keys, nodes)"
          />
        </a-tab-pane>
        <a-tab-pane key="3" tab="按流域">
          <TreeGeneral
            v-if="treeTabKey === '3' && !!riverTreeOptions.dataSource.length"
            :key="3"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :currentKeys="currentKeys"
            :treeOptions="riverTreeOptions"
            @onTreeMounted="onTreeMounted"
            @check="(keys, nodes) => clickTreeNode(keys, nodes)"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <div class="history-search-wrapper">
        <a-row>
          <label>统计类型：</label>
          <a-radio-group default-value="2" v-model:value="queryParam.type" @change="changeTimeType">
            <a-radio-button value="2">逐日</a-radio-button>
            <a-radio-button value="3">逐月</a-radio-button>
            <a-radio-button value="5">逐年</a-radio-button>
          </a-radio-group>
        </a-row>
        <a-row class="rain-row date-row">
          <div class="rain-select">
            <label>选择日期：</label>
            <a-range-picker
              v-if="queryParam.type == '2'"
              v-model="dayRange"
              format="YYYY-MM-DD"
              :allowClear="false"
              :disabled-date="disabledDate"
              valueFormat="YYYY-MM-DD"
              @change="onChangeDay"
            />
            <a-range-picker
              v-else-if="queryParam.type == '3'"
              :placeholder="['开始', '结束']"
              format="YYYY-MM"
              valueFormat="YYYY-MM"
              :value="monthRange"
              :mode="modeMonth"
              :disabled-date="disabledDate"
              @panelChange="handlePanelChangeMonth"
            />
            <a-range-picker
              v-else-if="queryParam.type == '5'"
              :placeholder="['开始', '结束']"
              format="YYYY"
              valueFormat="YYYY"
              :value="yearRange"
              :mode="modeYear"
              @panelChange="handlePanelChangeYear"
            />
            <!-- <a-select class="marL10" default-value=">" style="width: 120px" @change="handleChangeOperation">
              <a-select-option value=">">></a-select-option>
              <a-select-option value="≥">≥</a-select-option>
              <a-select-option value="<">&lt;</a-select-option>
              <a-select-option value="≤">≤</a-select-option>
            </a-select> -->
          </div>
          <div class="btn-group">
            <a-button type="primary" @click="handleSearch()">
              <a-icon type="search" />
              查 询
            </a-button>
            <a-button class="marL10" type="primary" @click="handleReset()">
              <a-icon type="reset" />
              重 置
            </a-button>
          </div>
        </a-row>
        <a-row class="rain-row">
          <a-radio-group v-model="currentTab" @change="onChangeTab">
            <a-radio :value="1">表单</a-radio>
            <a-radio :value="2">图表</a-radio>
          </a-radio-group>
          <a-button class="btn-group" type="primary" :loading="exportLoading" icon="download" @click="handleExport">
            导出
          </a-button>
        </a-row>
      </div>
      <div class="history-content">
        <div class="history-table" style="padding-top: 10px">
          <VxeTable
            :key="tableKey"
            v-if="currentTab == 1 && columns.length"
            ref="vxeTableRef"
            border="full"
            :isDrop="false"
            :isShowTableHeader="false"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :otherHeight="160"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
            show-footer
            :footer-method="footerMethod"
            :footer-row-style="{ background: '#F8F8F9' }"
          ></VxeTable>
          <div class="history-chart" id="rainfallChart" v-if="currentTab == 2"></div>
          <div class="chart-title-wrapper" v-if="currentTab == 2">
            <p>
              时段最高雨量（mm）: {{ statisticsRainFall.value }}{{ statisticsRainFall.name
              }}{{ statisticsRainFall.time }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <FormDrawer
      v-if="showFormDrawer"
      ref="formDrawerRef"
      :districtOptions="districtTreeOptions.dataSource"
      :projectOptions="projectOptions"
      :siteOptions="treeOptions.dataSource"
      @ok="onOperationComplete"
      @close="showFormDrawer = false"
    />
  </div>
</template>

<script lang="jsx">
  import { getRainFallPage } from './services'
  import { getSiteDistrictTree, getSiteRiverTree, getSiteCategoryTree } from '@/api/common'
  import FormDrawer from './modules/FormDrawer'
  import TreeGeneral from '@/components/TreeGeneral/multiTree.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import * as echarts from 'echarts/core'
  import { rainOption } from '../historyChart'
  import { getFixedNum } from '@/utils/dealNumber'
  import excelExport from '@/utils/excelExport.js'

  export default {
    name: 'Flow',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      FormDrawer,
    },
    data() {
      return {
        treeTabKey: '1',
        exportLoading: false,
        currentKeys: [],
        nodes: [],
        categoryTreeOptions: {
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
          },
        },
        districtTreeOptions: {
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
          },
        },
        riverTreeOptions: {
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
          },
        },
        showFormDrawer: false,
        projectOptions: [],

        tableData: [],
        tableTitle: '',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,
        dayRange: [moment(), moment()],
        monthRange: [moment(), moment()],
        yearRange: [moment(), moment()],
        modeMonth: ['month', 'month'],
        modeYear: ['year', 'year'],
        pageSize: 10,
        queryParam: {
          startTime: moment().format('YYYY-MM-DD') + ' 00:00:00',
          endTime: moment().format('YYYY-MM-DD') + ' 23:59:59',
          type: '2', //类型(1逐时 2逐日 3逐月 4瞬时)年5
          pageNum: 1,
          pageSize: 10,
          siteIds: [13],
        },
        currentTab: 1,
        districtTypes: {},
        siteTypes: {},

        //表格
        tableKey: 1,
        columns: [],
        list: [],
        statisticsRainFall: {
          value: null,
          name: '',
          time: '',
        },
      }
    },
    created() {
      let param = { labels: '1', indexCode: 'rainfall' }
      getSiteCategoryTree(param).then(res => {
        this.categoryTreeOptions.dataSource = res.data
      })
      getSiteDistrictTree(param).then(res => {
        this.districtTreeOptions.dataSource = res.data
      })
      getSiteRiverTree(param).then(res => {
        this.riverTreeOptions.dataSource = res.data
      })
    },
    mounted() {
      this.getList()
    },
    watch: {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.pageSize = pageSize
        // this.getList()
      },
      //切换表格
      onChangeTab() {
        if (this.currentTab == 1) {
          // this.queryParam.pageSize = 10
          this.getList()
        } else if (this.currentTab == 2) {
          this.getRainChart()
        }
      },
      handleReset() {
        this.queryParam = {
          startTime: moment().format('YYYY-MM-DD') + ' 00:00:00',
          endTime: moment().format('YYYY-MM-DD') + ' 23:59:59',
          type: '2',
          pageNum: 1,
          pageSize: this.pageSize, //10,
          siteIds: [13],
        }
        this.onChangeTab()
      },
      //切换日期
      onChangeDay() {
        this.queryParam.startTime = this.dayRange[0] + ' 00:00:00'
        this.queryParam.endTime = this.dayRange[1] + ' 23:59:59'
      },
      disabledDate(val) {
        return val > moment().subtract(0, 'day')
      },
      meanNum(list, field) {
        let count = 0
        list.forEach(item => {
          count += Number(item[field])
        })
        return count / list.length
      },
      handlePanelChangeMonth(value, mode) {
        this.monthRange = value
        this.queryParam.startTime = value[0].format('YYYY-MM') + '-01 00:00:00'
        this.queryParam.endTime = value[1].format('YYYY-MM-DD') + ' 23:59:59'
        this.modeMonth = [mode[0] === 'date' ? 'month' : mode[0], mode[1] === 'date' ? 'month' : mode[1]]
      },
      handlePanelChangeYear(value, mode) {
        this.yearRange = value
        this.queryParam.startTime = value[0].format('yyyy') + '-01-01 00:00:00'
        this.queryParam.endTime = value[1].format('yyyy') + '-12-31 23:59:59'
      },
      footerMethod() {
        // 接收二维数组
        return this.gridOptions.footerData
      },
      //切换运算符号
      handleChangeOperation() {},
      changeTimeType() {
        if (this.queryParam.type == '2') {
          this.queryParam.startTime = moment().format('YYYY-MM-DD') + ' 00:00:00'
          this.queryParam.endTime = moment().format('YYYY-MM-DD') + ' 23:59:59'
        } else if (this.queryParam.type == '3') {
          this.queryParam.startTime = moment().format('YYYY-MM') + '-01 00:00:00'
          this.queryParam.endTime = moment().format('YYYY-MM-DD') + ' 23:59:59'
        } else if (this.queryParam.type == '5') {
          this.queryParam.startTime = moment().format('YYYY') + '-01-01 00:00:00'
          this.queryParam.endTime = moment().format('YYYY') + '-12-31 23:59:59'
        }
        this.onChangeTab()
      },
      //查询
      handleSearch() {
        this.onChangeTab()
      },
      /** 查询列表 */
      getList(isExport, callback) {
        if (!isExport) {
          this.showFormDrawer = false
          this.loading = true
        }
        getRainFallPage({
          ...this.queryParam,
          pageSize: isExport ? Number.MAX_SAFE_INTEGER : this.queryParam.pageSize,
        }).then(response => {
          if (response?.data == null) {
            this.list = []
            this.total = 0
            this.loading = false
            return
          }

          const columns = [
            { key: 'seq', type: 'seq', title: '序号', minWidth: 50, fixed: 'left' },
            { key: 'siteName', field: 'siteName', title: '站点名称', minWidth: 120, fixed: 'left' },
            {
              key: 'objectCategoryName',
              field: 'objectCategoryName',
              title: '站点类型',
              minWidth: 180,
              fixed: 'left',
            },
            {
              key: 'sumRainFall',
              field: 'sumRainFall',
              title: '累积雨量（mm)',
              minWidth: 200,
              fixed: 'left',
            },
          ]
          const list = response.data.data
          if (list?.length) {
            for (let j = 0; j < list[0]?.historyValue?.length; j++) {
              columns.push({
                key: list[0].siteId,
                field: `indexValue_[${[j]}`,
                title: list[0].historyValue[j].dateTime,
                minWidth: 98,
              })
            }
            for (let i = 0; i < list.length; i++) {
              list[i].sumRainFall = getFixedNum(list[i].sumRainFall, 1)
              for (let j = 0; j < list[i]?.historyValue?.length; j++) {
                list[i][`indexValue_[${[j]}`] = getFixedNum(list[i].historyValue[j].indexValue, 1)
              }
            }
          }

          if (isExport) {
            callback(columns, list)
          } else {
            this.tableKey += 1
            this.total = response.data.total
            this.loading = false
            this.list = list
            this.columns = columns
          }
        })
      },
      getRainChart() {
        const params = this.queryParam
        params.pageSize = Number.MAX_SAFE_INTEGER
        getRainFallPage(params).then(res => {
          if (res.data == null) {
            const rainfallChart = echarts.init(document.getElementById('rainfallChart'))
            rainfallChart.clear()
            return
          }
          if (res?.data?.data) {
            const color = [
              '#507EF7',
              '#42d4f4',
              '#3cb44b',
              '#911eb4',
              '#bfef45',
              '#fabed4',
              '#469990',
              '#dcbeff',
              '#9A6324',
              '#aaffc3',
              '#808000',
              '#000075',
              '#a9a9a9',
            ]
            setTimeout(() => {
              const rainfallChart = echarts.init(document.getElementById('rainfallChart'))
              rainfallChart.clear()
              rainOption.series = []
              let rainRallDate = [],
                rainRallLegend = []
              for (let j = 0; j < res.data.data[0]?.historyValue?.length; j++) {
                rainRallDate.push(res?.data?.data[0].historyValue[j].dateTime)
              }
              this.statisticsRainFall.value = res?.data?.data[0].sumRainFall
              for (let i = 0; i < res.data.data.length; i++) {
                rainRallLegend.push(res?.data?.data[i].siteName)
                res.data.data[i].rainRallArr = []
                if (this.statisticsRainFall.value <= res?.data?.data[i].sumRainFall) {
                  this.statisticsRainFall.value = getFixedNum(res?.data?.data[i].sumRainFall, 1)
                  this.statisticsRainFall.name = res?.data?.data[i].siteName
                }
                for (let j = 0; j < res.data.data[i]?.historyValue?.length; j++) {
                  res?.data?.data[i].rainRallArr.push(
                    res.data.data[i].historyValue[j].indexValue == null
                      ? 0
                      : getFixedNum(res.data.data[i].historyValue[j].indexValue, 1),
                  )
                }
                rainOption.series.push({
                  name: res?.data?.data[i].siteName,
                  type: 'bar',
                  barMaxWidth: 10,
                  itemStyle: {
                    borderRadius: 11,
                  },
                  data: res?.data?.data[i].rainRallArr,
                })
              }

              rainOption.color = color
              rainOption.legend.data = rainRallLegend
              rainOption.xAxis.data = rainRallDate
              if (rainOption.legend.data.length > 15) {
                rainOption.dataZoom[0].show = true
              } else {
                rainOption.dataZoom[0].show = false
              }
              rainfallChart.setOption(rainOption)
            }, 600)
          }
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          siteCode: undefined,
          siteName: undefined,
          pageNum: 1,
          // pageSize: 10,
          sort: [],
        }
        this.handleQuery()
      },
      meanNum(list, field) {
        let count = 0
        list.forEach(item => {
          count += Number(item[field])
        })
        return count / list.length
      },
      footerMethod({ columns, data }) {
        const footerData = [
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '平均雨量(mm)：'
            }
            if (_columnIndex > 2) {
              return getFixedNum(this.meanNum(data, column.property), 1)
            }
            return null
          }),
        ]
        return footerData
      },
      // 操作完成后
      onOperationComplete() {
        this.getList()
        // this.$refs.treeGeneralRef.getDataSource()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 树加载完成后
      onTreeMounted(checkedKeys, nodes) {
        this.currentKeys = checkedKeys
        this.nodes = nodes
        this.queryParam.siteIds = checkedKeys.map(el => el.slice(1, el.length))

        this.handleQuery()
      },
      clickTreeNode(checkedKeys, nodes) {
        if (checkedKeys.length == 0) {
          this.queryParam.siteIds = []
          this.onChangeTab()
          return
        }
        this.currentKeys = checkedKeys
        this.nodes = nodes
        this.queryParam.siteIds = checkedKeys.map(el => el.slice(1, el.length))

        this.onChangeTab()
      },

      onClickRow(record, index) {
        this.handleUpdate(record, '')
      },

      handleTableChange(pagination, filters, sorter) {
        this.getList()
      },

      // 导出
      handleExport() {
        this.exportLoading = true
        this.getList(true, (columns, data) => {
          this.exportLoading = false

          excelExport(
            columns.slice(1, columns.length),
            data,
            `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`,
          )
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('../history.less');
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;

      .ant-tabs-nav-container {
        height: auto;

        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
