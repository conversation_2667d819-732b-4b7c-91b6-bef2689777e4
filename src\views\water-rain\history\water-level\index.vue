<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <a-tabs v-model="treeTabKey" @change="key => (treeTabKey = key)">
        <a-tab-pane key="1" tab="按站点">
          <TreeGeneral
            v-if="treeTabKey === '1' && !!categoryTreeOptions.dataSource.length"
            :key="1"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :currentKeys="currentKeys"
            :treeOptions="categoryTreeOptions"
            @onTreeMounted="onTreeMounted"
            @check="(keys, nodes) => clickTreeNode(keys, nodes)"
          />
        </a-tab-pane>
        <a-tab-pane key="2" tab="按区域">
          <TreeGeneral
            v-if="treeTabKey === '2' && !!districtTreeOptions.dataSource.length"
            :key="2"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :currentKeys="currentKeys"
            :treeOptions="districtTreeOptions"
            @onTreeMounted="onTreeMounted"
            @check="(keys, nodes) => clickTreeNode(keys, nodes)"
          />
        </a-tab-pane>
        <a-tab-pane key="3" tab="按流域">
          <TreeGeneral
            v-if="treeTabKey === '3' && !!riverTreeOptions.dataSource.length"
            :key="3"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :currentKeys="currentKeys"
            :treeOptions="riverTreeOptions"
            @onTreeMounted="onTreeMounted"
            @check="(keys, nodes) => clickTreeNode(keys, nodes)"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <div class="history-search-wrapper">
        <a-row>
          <label>统计类型：</label>
          <a-radio-group default-value="2" v-model:value="queryParam.type" @change="changeTimeType">
            <a-radio-button value="2">逐日</a-radio-button>
            <a-radio-button value="3">逐月</a-radio-button>
            <a-radio-button value="6">旬</a-radio-button>
          </a-radio-group>
        </a-row>
        <a-row class="rain-row date-row">
          <div class="rain-select">
            <label>选择日期：</label>
            <a-range-picker
              v-if="queryParam.type == '2'"
              v-model="timeRange"
              format="YYYY-MM-DD"
              :allowClear="false"
              :disabled-date="disabledDate"
              valueFormat="YYYY-MM-DD"
              @change="onChangeRange"
            />
            <a-range-picker
              v-else-if="queryParam.type == '3' || queryParam.type == '6'"
              :placeholder="['开始', '结束']"
              format="YYYY-MM"
              valueFormat="YYYY-MM"
              :value="monthRange"
              :mode="modeMonth"
              :disabled-date="disabledDate"
              @panelChange="handlePanelChangeMonth"
            />
          </div>
          <div class="btn-group">
            <a-button type="primary" @click="handleSearch()">
              <a-icon type="search" />
              查 询
            </a-button>
            <a-button class="marL10" type="primary" @click="handleReset()">
              <a-icon type="reset" />
              重 置
            </a-button>
          </div>
        </a-row>
        <a-row class="rain-row">
          <a-radio-group v-model="currentTab" @change="onChangeTab">
            <a-radio :value="1">表单</a-radio>
            <a-radio :value="2">图表</a-radio>
          </a-radio-group>
          <a-button class="btn-group" type="primary" :loading="exportLoading" icon="download" @click="handleExport">
            导出
          </a-button>
        </a-row>
      </div>
      <div class="history-content">
        <div class="history-table" style="padding-top: 10px">
          <VxeTable
            :key="tableKey"
            v-if="currentTab == 1 && columns.length"
            ref="vxeTableRef"
            border="full"
            :isDrop="false"
            :isShowTableHeader="false"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :otherHeight="160"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
          >
            <div class="vxe-table-footer" slot="footer">
              <div class="footer-box">
                <div class="footer-item">
                  时段最高平均水位（m）：{{ statisticsLevel.maxInfo.value }}
                  {{ statisticsLevel.maxInfo.name }}
                  {{ statisticsLevel.maxInfo.time }}
                </div>
                <div class="footer-item">
                  时段最低平均水位（m）：{{ statisticsLevel.minInfo.value }}
                  {{ statisticsLevel.minInfo.name }}
                  {{ statisticsLevel.minInfo.time }}
                </div>
              </div>
            </div>
          </VxeTable>
          <div class="history-chart" id="waterLevelChart" v-if="currentTab == 2"></div>
          <div class="chart-title-wrapper" v-if="currentTab == 2">
            <p>
              时段最高平均水位（m）: {{ statisticsLevelChart.maxInfo.value }}
              <span class="marL10 marR10">{{ statisticsLevelChart.maxInfo.name }}</span>
              {{ statisticsLevel.minInfo.time }}
            </p>
            <p>
              时段最低平均水位（m）: {{ statisticsLevelChart.minInfo.value }}
              <span class="marL10 marR10">{{ statisticsLevelChart.minInfo.name }}</span>
              {{ statisticsLevel.minInfo.time }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <FormDrawer
      v-if="showFormDrawer"
      ref="formDrawerRef"
      :districtOptions="districtTreeOptions.dataSource"
      :projectOptions="projectOptions"
      :siteOptions="treeOptions.dataSource"
      @ok="onOperationComplete"
      @close="showFormDrawer = false"
    />
  </div>
</template>

<script lang="jsx">
  import { getWaterLevel } from './services'
  import { getSiteDistrictTree, getSiteRiverTree, getSiteCategoryTree } from '@/api/common'
  import FormDrawer from './modules/FormDrawer'
  import TreeGeneral from '@/components/TreeGeneral/multiTree.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import * as echarts from 'echarts/core'
  import { waterLevelOption } from '../historyChart'
  import { getFixedNum } from '@/utils/dealNumber'
  import excelExport from '@/utils/excelExport.js'

  export default {
    name: 'Flow',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      FormDrawer,
    },
    data() {
      return {
        treeTabKey: '1',
        exportLoading: false,
        currentKeys: [],
        nodes: [],
        categoryTreeOptions: {
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
          },
        },
        districtTreeOptions: {
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
          },
        },
        riverTreeOptions: {
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
          },
        },
        showFormDrawer: false,
        projectOptions: [],

        tableData: [],
        tableTitle: '',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,
        timeRange: [moment(), moment()],
        monthRange: [moment(), moment()],
        modeMonth: ['month', 'month'],
        pageSize: 10,
        queryParam: {
          startTime: moment().format('YYYY-MM-DD') + ' 00:00:00',
          endTime: moment().format('YYYY-MM-DD') + ' 23:59:59',
          type: '2', //类型(1逐时 2逐日 3逐月 4瞬时)年5
          pageNum: 1,
          pageSize: 10,
          siteIds: [13],
        },
        currentTab: 1,
        districtTypes: {},
        siteTypes: {},

        //表格
        tableKey: 1,
        columns: [],
        list: [],
        maxChartNum: 0,
        minChartNum: 0,
        statisticsLevel: {
          maxInfo: {
            value: null,
            name: '',
            time: '',
          },
          minInfo: {
            value: null,
            name: '',
            time: '',
          },
        },
        statisticsLevelChart: {
          maxInfo: {
            value: null,
            name: '',
          },
          minInfo: {
            value: null,
            name: '',
          },
        },
      }
    },
    created() {
      let param = { labels: '1', indexCode: 'waterLevel' }
      getSiteCategoryTree(param).then(res => {
        this.categoryTreeOptions.dataSource = res.data
      })
      getSiteDistrictTree(param).then(res => {
        this.districtTreeOptions.dataSource = res.data
      })
      getSiteRiverTree(param).then(res => {
        this.riverTreeOptions.dataSource = res.data
      })
    },
    mounted() {
      this.getList()
    },
    watch: {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.pageSize = pageSize
        // this.getList()
      },
      //切换表格
      onChangeTab() {
        if (this.currentTab == 1) {
          // this.queryParam.pageSize = 10
          this.getList()
        } else if (this.currentTab == 2) {
          this.getRainChart()
        }
      },
      handleReset() {
        this.queryParam = {
          startTime: moment().format('YYYY-MM-DD') + ' 00:00:00',
          endTime: moment().format('YYYY-MM-DD') + ' 23:59:59',
          type: '2',
          pageNum: 1,
          // pageSize: 10,
          pageSize: this.pageSize,
          siteIds: [13],
        }
        this.onChangeTab()
      },
      //切换日期
      onChangeRange() {
        this.queryParam.startTime = this.timeRange[0] + ' 00:00:00'
        this.queryParam.endTime = this.timeRange[1] + ' 23:59:59'
      },
      handlePanelChangeMonth(value, mode) {
        this.monthRange = value
        this.queryParam.startTime = value[0].format('YYYY-MM') + '-01 00:00:00'
        this.queryParam.endTime = value[1].format('YYYY-MM-DD') + ' 23:59:59'
        this.modeMonth = [mode[0] === 'date' ? 'month' : mode[0], mode[1] === 'date' ? 'month' : mode[1]]
      },
      changeTimeType() {
        if (this.queryParam.type == '2') {
          this.queryParam.startTime = moment().format('YYYY-MM-DD') + ' 00:00:00'
          this.queryParam.endTime = moment().format('YYYY-MM-DD') + ' 23:59:59'
        } else if (this.queryParam.type == '3' || this.queryParam.type == '6') {
          this.queryParam.startTime = moment().format('YYYY-MM') + '-01 00:00:00'
          this.queryParam.endTime = moment().format('YYYY-MM-DD') + ' 23:59:59'
        }
        this.getList()
      },
      //查询
      handleSearch() {
        this.onChangeTab()
      },
      /** 查询列表 */
      getList(isExport, callback) {
        if (!isExport) {
          this.showFormDrawer = false
          this.loading = true
        }
        getWaterLevel({
          ...this.queryParam,
          pageSize: isExport ? Number.MAX_SAFE_INTEGER : this.queryParam.pageSize,
        }).then(response => {
          this.loading = false
          if (response?.data == null) {
            this.list = []
            this.total = 0
            return
          }

          const columns = [
            { key: 'seq', type: 'seq', title: '序号', width: 50, fixed: 'left' },
            { key: 'siteName', field: 'siteName', title: '站点名称', minWidth: 120, fixed: 'left' },
            {
              key: 'objectCategoryName',
              field: 'objectCategoryName',
              title: '站点类型',
              minWidth: 180,
              fixed: 'left',
            },
            {
              key: 'maxWaterLevel',
              field: 'maxWaterLevel',
              title: '时段最高水位(m）',
              minWidth: 200,
              fixed: 'left',
            },
            {
              key: 'minWaterLevel',
              field: 'minWaterLevel',
              title: '时段最低水位（m）',
              minWidth: 200,
              fixed: 'left',
            },
          ]
          const list = response.data.data

          if (list?.length) {
            this.statisticsLevel.maxInfo.time = list[0]?.maxWaterLevelTime
            this.statisticsLevel.minInfo.time = list[0]?.minWaterLevelTime
            this.statisticsLevel.maxInfo.value = list[0]?.maxWaterLevel
            this.statisticsLevel.minInfo.value = list[0]?.minWaterLevel
            for (let j = 0; j < list[0]?.historyValue?.length; j++) {
              columns.push({
                key: list[0].siteId,
                field: `indexValue_[${[j]}`,
                title: list[0].historyValue[j].dateTime,
                minWidth: 98,
              })
            }
            for (let i = 0; i < list.length; i++) {
              //统计
              if (this.statisticsLevel.maxInfo.value <= list[i].maxWaterLevel) {
                this.statisticsLevel.maxInfo.value = getFixedNum(list[i].maxWaterLevel, 2)
                this.statisticsLevel.maxInfo.name = list[i].siteName
              }
              if (this.statisticsLevel.minInfo.value >= list[i].minWaterLevel) {
                this.statisticsLevel.minInfo.value = getFixedNum(list[i].minWaterLevel, 2)
                this.statisticsLevel.minInfo.name = list[i].siteName
              }
              list[i].maxWaterLevel = getFixedNum(list[i]?.maxWaterLevel, 2)
              list[i].minWaterLevel = getFixedNum(list[i]?.minWaterLevel, 2)
              for (let j = 0; j < list[i]?.historyValue?.length; j++) {
                list[i][`indexValue_[${[j]}`] = getFixedNum(list[i].historyValue[j].indexValue, 2)
              }
            }

            if (isExport) {
              callback(columns, list)
            } else {
              this.tableKey += 1
              this.total = response.data.total
              this.loading = false
              this.columns = columns
              this.list = list
            }
          }
        })
      },
      getRainChart() {
        const params = this.queryParam
        params.pageSize = Number.MAX_SAFE_INTEGER
        getWaterLevel(params).then(res => {
          if (res.data == null) {
            const waterLevelChart = echarts.init(document.getElementById('waterLevelChart'))
            waterLevelChart.clear()
            return
          }
          if (res?.data?.data) {
            const color = [
              '#EF8432',
              '#58A9FB',
              '#B5E241',
              '#4363d8',
              '#911eb4',
              '#00A316',
              '#fabed4',
              '#469990',
              '#dcbeff',
              '#9A6324',
              '#aaffc3',
              '#808000',
              '#000075',
              '#a9a9a9',
            ]

            setTimeout(() => {
              const waterLevelChart = echarts.init(document.getElementById('waterLevelChart'))
              waterLevelChart.clear()
              waterLevelOption.series = []
              let waterLevelDate = [],
                waterLevelLegend = []
              for (let j = 0; j < res?.data?.data[0]?.historyValue?.length; j++) {
                waterLevelDate.push(res?.data?.data[0].historyValue[j].dateTime)
              }
              this.statisticsLevelChart.maxInfo.value = this.minChartNum = res?.data?.data[0].maxWaterLevel
              this.statisticsLevelChart.minInfo.value = this.minChartNum = res?.data?.data[0].minWaterLevel
              for (let i = 0; i < res?.data?.data.length; i++) {
                waterLevelLegend.push(res?.data?.data[i].siteName)
                res.data.data[i].waterLevelArr = []
                //统计
                if (this.statisticsLevelChart.maxInfo.value <= res?.data?.data[i].maxWaterLevel) {
                  this.statisticsLevelChart.maxInfo.value = getFixedNum(res?.data?.data[i].maxWaterLevel, 2)
                  this.statisticsLevelChart.maxInfo.name = res?.data?.data[i].siteName
                }
                if (this.statisticsLevelChart.minInfo.value >= res?.data?.data[i].minWaterLevel) {
                  this.statisticsLevelChart.minInfo.value = getFixedNum(res?.data?.data[i].minWaterLevel, 2)
                  this.statisticsLevelChart.minInfo.name = res?.data?.data[i].siteName
                }
                for (let j = 0; j < res?.data?.data[i]?.historyValue?.length; j++) {
                  res?.data?.data[i].waterLevelArr.push(
                    res?.data?.data[i].historyValue[j].indexValue == null
                      ? 0
                      : getFixedNum(res?.data?.data[i].historyValue[j].indexValue, 2),
                  )
                }
                waterLevelOption.series.push({
                  name: res?.data?.data[i].siteName,
                  type: 'line',
                  smooth: true,
                  symbol: 'emptyCircle',
                  symbolSize: 8,
                  lineStyle: {
                    width: 2,
                  },
                  // itemStyle: {
                  //   borderWidth: 1,
                  //   color: waterLevelColors[i]?.color[1] ? waterLevelColors[i].color[1] : '#e2a406',
                  // },
                  data: res?.data?.data[i].waterLevelArr,
                })
              }
              waterLevelOption.color = color
              waterLevelOption.legend.data = waterLevelLegend
              waterLevelOption.xAxis.data = waterLevelDate
              if (waterLevelOption.legend.data.length > 15) {
                waterLevelOption.dataZoom[0].show = true
              } else {
                waterLevelOption.dataZoom[0].show = false
              }
              waterLevelChart.setOption(waterLevelOption)
            }, 500)
          }
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.onChangeTab()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          siteCode: undefined,
          siteName: undefined,
          pageNum: 1,
          // pageSize: 10,
          sort: [],
        }
        this.handleQuery()
      },
      disabledDate(val) {
        return val > moment().subtract(0, 'day')
      },
      // 操作完成后
      onOperationComplete() {
        this.getList()
        // this.$refs.treeGeneralRef.getDataSource()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 树加载完成后
      onTreeMounted(checkedKeys, nodes) {
        this.currentKeys = checkedKeys
        this.nodes = nodes
        this.queryParam.siteIds = checkedKeys.map(el => el.slice(1, el.length))

        this.handleQuery()
      },
      // 树加载
      clickTreeNode(checkedKeys, nodes) {
        if (checkedKeys.length == 0) {
          this.queryParam.siteIds = []
          this.onChangeTab()
          return
        }
        this.currentKeys = checkedKeys
        this.nodes = nodes
        this.queryParam.siteIds = checkedKeys.map(el => el.slice(1, el.length))

        this.onChangeTab()
      },

      onClickRow(record, index) {
        this.handleUpdate(record, '')
      },

      handleTableChange(pagination, filters, sorter) {
        this.getList()
      },

      // 导出
      handleExport() {
        this.exportLoading = true
        this.getList(true, (columns, data) => {
          this.exportLoading = false

          excelExport(
            columns.slice(1, columns.length),
            data,
            `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`,
          )
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('../history.less');
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;

      .ant-tabs-nav-container {
        height: auto;

        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
