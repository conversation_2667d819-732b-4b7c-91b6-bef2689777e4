<template>
  <div style="height: 100%">
    <a-input placeholder="请输入" @change="onChange" allowClear />
    <div :class="hasTab ? 'tab-tree-panel-box' : 'tree-panel-tree-box'">
      <div class="loading" v-if="loading">
        <a-spin />
      </div>
      <AntTree
        v-if="treeData.length > 0"
        :tree-data="treeData"
        :default-expanded-keys="expandedKeys"
        :expanded-keys="expandedKeys"
        :auto-expand-parent="autoExpandParent"
        :showIcon="false"
        showLine
        @select="handleNodeClick"
        @expand="onExpand"
        :selectedKeys="selectedKeys"
      >
        <a-icon slot="switcherIcon" type="caret-down" />
        <template slot="title" slot-scope="node" class="tempStyle">
          <div class="container">
            <span v-if="node.title.indexOf(searchValue) > -1" class="name">
              {{ node.title.substr(0, node.title.indexOf(searchValue)) }}
              <span style="color: #f50">
                {{ searchValue }}
              </span>
              {{ node.title.substr(node.title.indexOf(searchValue) + searchValue.length) }}
            </span>
            <span v-else class="name" :title="node.title">
              {{ node.title }}
            </span>
            <!-- <a-icon
              type="plus"
              v-show="node.groupId == undefined"
              class="plusType"
              style="padding-left: 30px"
              @click.stop="nodeHandle(node, true)"
            ></a-icon>
            <a-icon
              type="edit"
              v-show="node.groupId != undefined"
              class="editType"
              style="padding-left: 10px"
              @click.stop="nodeHandle(node, false)"
            ></a-icon>
            <a-icon
              type="delete"
              v-show="node.groupId != undefined"
              class="deleteType"
              style="padding-left: 10px"
              @click.stop="nodeDel(node)"
            ></a-icon> -->
          </div>
        </template>
      </AntTree>
    </div>

    <a-modal
      v-model="visible"
      :title="modalTitle"
      :ok-text="modalOkText"
      :cancel-text="modalCancelText"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="班组名称：" prop="groupName">
              <a-input allowClear v-model="form.groupName" placeholder="请输入" />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="上级类别" prop="shiftId">
              <a-tree-select
                v-model="form.shiftId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="treeData"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'title',
                  key: 'key',
                  value: 'key',
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="所属组织" prop="deptId">
              <a-tree-select
                v-model="form.deptId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="configTreeOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'deptName',
                  key: 'deptId',
                  value: 'deptId',
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="备注：">
              <a-textarea v-model="form.remark" placeholder="请输入" allowClear />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script lang="jsx">
  import AntTree from 'ant-design-vue/es/tree'
  // import {getConfigTree,addPatrolCategory,updatePatrolCategory,deletePatrolCategory} from '../../views/patrolwork/patrol-item/services'
  import {
    getConfigTree,
    addPatrolCategory,
    updatePatrolCategory,
    deletePatrolCategory,
    getObjectCategoryTree,
  } from '../services.js'
  import { getTreeByLoginOrgId } from '@/api/common'

  import getFlatTreeMap from '@/utils/getMapFlatTree'
  const getParentKey = (key, tree) => {
    let parentKey
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i]
      // console.log(node)
      if (node.children) {
        if (node.children.some(item => item.key === key)) {
          parentKey = node.key
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children)
        }
      }
    }
    return parentKey
  }

  function handleTreeData(data) {
    data.forEach(item => {
      item['disabled'] = item.isLeaf
      if (item.children) {
        handleTreeData(item.children)
      }
    })
    return data
  }

  function resetDataSource(data, replaceFields) {
    function dealData(arr) {
      // console.log(arr)
      arr.forEach((ele, i) => {
        // console.log(ele)
        arr[i] = {
          ...ele,
          key: ele?.[replaceFields.key] || ele.shiftId,
          title: ele?.[replaceFields.title] || ele.shiftName,
          children: ele?.[replaceFields.children],
          scopedSlots: { title: 'title' },
        }

        dealData(ele?.[replaceFields.children] || [])
      })
    }
    dealData(data)

    return data
  }

  export default {
    name: 'TreeCategory',
    components: { AntTree },
    props: {
      treeOptions: {
        type: Object,
        required: true,
      },
      isLeafDisabled: {
        type: Boolean,
      },
      defaultExpandedKeys: {
        type: Array,
        default: () => [],
      },
      hasTab: {
        type: Boolean,
        default: false,
      },
    },

    data() {
      return {
        loading: false,
        treeData: [],

        expandedKeys: this.defaultExpandedKeys,
        key: this.treeOptions.replaceFields.key,
        leafNodes: [],
        searchValue: '',
        autoExpandParent: true,

        dataList: [],

        visible: false,
        modalType: '',
        modalTitle: '',
        modalOkText: '',
        modalCancelText: '',
        form: {
          deptId: null,
          groupName: null,
          remark: null,
          shiftId: null,
          groupId: undefined,
        },
        selectedKeys: [],
        configTreeOptions: [],
        rules: {
          groupName: [{ required: true, message: '班组名称不能为空', trigger: 'change' }],
          shiftId: [{ required: true, message: '上级类别不能为空', trigger: 'change' }],
          deptId: [{ required: true, message: '所属部门不能为空', trigger: 'change' }],
        },
      }
    },
    filters: {},
    created() {
      this.getDataSource(undefined, 'created')
      this.getOrgTree()
    },
    watch: {},
    methods: {
      /** 查询部门下拉树结构 */
      getOrgTree() {
        getTreeByLoginOrgId().then(res => {
          this.configTreeOptions = res?.data
          this.configTreeTypes = getFlatTreeMap(res.data || [], 'deptId')
        })
      },
      // 班组新增/编辑
      nodeHandle(node, type) {
        this.modalType = type
        if (type) {
          this.modalTitle = '新增'
          this.modalOkText = '确定'
          this.form.groupId = node.groupId
        } else if (!type) {
          this.modalTitle = '编辑'
          this.form.deptId = node.deptId
          this.form.groupName = node.groupName
          this.form.remark = node.remark
          let shiftId
          for (var i = 0; i < this.treeData.length; i++) {
            for (var j = 0; j < this.treeData[i].children.length; j++) {
              if (node.groupId == this.treeData[i].children[j].groupId) {
                shiftId = this.treeData[i].shiftId
              }
            }
          }
          this.form.shiftId = shiftId
          this.form.groupId = node.groupId
          this.modalOkText = '确定'
        }
        this.visible = true
      },
      handleOk() {
        if (this.modalType) {
          let addParam = {
            deptId: this.form.deptId,
            groupName: this.form.groupName,
            remark: this.form.remark,
            shiftId: this.form.shiftId,
          }
          addPatrolCategory(addParam).then(res => {
            this.$message.success(`新增成功`, 3)
            this.getDataSource(undefined, 'search')
            this.visible = false
          })
        } else if (!this.modalType) {
          let updateParam = {
            deptId: this.form.deptId,
            groupName: this.form.groupName,
            remark: this.form.remark,
            groupId: this.form.groupId,
            shiftId: this.form.shiftId,
          }
          updatePatrolCategory(updateParam).then(res => {
            this.$message.success(`更新成功`, 3)
            this.getDataSource(undefined, 'search')
            this.visible = false
          })
        }
      },
      // 班组删除
      nodeDel(node) {
        var that = this
        const Ids = node.groupId
        const names = node.title
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deletePatrolCategory({ groupIds: Ids }).then(res => {
              //that.treeData = that.treeData.filter(item => item.groupId != Ids)
              let newArr = that.removeNodeInTree(that.treeData, Ids)
              that.treeData = newArr
              that.$message.success(`成功删除` + res.data + `条数据`, 3)
              that.getDataSource(undefined, 'search')

              that.visible = false
            })
          },
          onCancel() {},
        })
      },
      removeNodeInTree(treeList, id) {
        // 通过id从数组（树结构）中移除元素
        if (!treeList || !treeList.length) {
          return
        }
        for (let i = 0; i < treeList.length; i++) {
          if (treeList[i].id == id) {
            treeList.splice(i, 1)
            break
          }
          this.removeNodeInTree(treeList[i].children, id)
        }
      },
      handleCancel() {
        this.visible = false
      },
      getExpandedKeys(nodes) {
        if (!nodes || nodes.length === 0) {
          return []
        }

        nodes.forEach(node => {
          this.leafNodes.push(node.key)
          return this.getExpandedKeys(node.children)
        })
      },
      generateList(data) {
        for (let i = 0; i < data.length; i++) {
          const node = data[i]
          const key = node.key
          const title = node.title
          this.dataList.push({ key, title })
          if (node.children) {
            this.generateList(node.children)
          }
        }
      },
      // 筛选节点
      onChange(e) {
        const value = e.target.value
        const expandedKeys = this.dataList
          .map(item => {
            if (item.title.indexOf(value) > -1) {
              return getParentKey(item.key, this.treeData)
            }
            return null
          })
          .filter((item, i, self) => item && self.indexOf(item) === i)

        Object.assign(this, {
          expandedKeys,
          searchValue: value,
          autoExpandParent: true,
        })
      },
      // 获取树
      getDataSource(value, type) {
        this.loading = true

        if (this.treeOptions.dataSource?.length && type !== 'search') {
          this.treeData = resetDataSource(this.treeOptions.dataSource, this.treeOptions.replaceFields)
          // 设置默认选中第一个节点
          if (this.selectedKeys.length == 0) {
            this.selectedKeys = [this.treeData[0].key]
          }
          this.generateList(this.treeData)

          this.getExpandedKeys(this.treeData)
          Object.assign(this, {
            expandedKeys: this.leafNodes,
            searchValue: value,
            autoExpandParent: true,
          })
          this.leafNodes = []
          if (type === 'created') {
            this.$emit('onTreeMounted', this.treeData)
            this.$emit('ok')
          }
        } else {
          getObjectCategoryTree()
            .then(response => {
              // if (this.isLeafDisabled) {
              //   this.treeData = handleTreeData(response?.data || [])
              // }
              let tmpTreeData = (response?.data || []).map(ele => ({
                ...ele,
                name: ele.shiftName,
                children: ele.groups.map(el => ({ ...el, name: el.groupName, isLeaf: true })),
              }))
              this.treeData = resetDataSource(tmpTreeData || [], this.treeOptions.replaceFields)
              // 设置默认选中第一个节点
              if (this.selectedKeys.length == 0) {
                this.selectedKeys = [this.treeData[0].key]
              }
              this.generateList(this.treeData)

              this.getExpandedKeys(response.data)
              Object.assign(this, {
                expandedKeys: this.leafNodes,
                searchValue: value,
                autoExpandParent: true,
              })

              this.leafNodes = []
              if (type === 'created') {
                this.$emit('onTreeMounted', response?.data || [])
                this.$emit('ok')
              }
              this.autoExpandParent = true
            })
            .catch(res => {
              console.log('error', res)
            })
        }
        this.loading = false
      },
      // 节点单击事件,
      handleNodeClick(keys, event) {
        if (!keys?.length) return
        this.selectedKeys = [event.node.eventKey]
        this.$emit('select', event.node)
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys
        this.autoExpandParent = false
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep li.ant-tree-treenode-disabled > .ant-tree-node-content-wrapper span {
    color: #aaa;
  }
  ::v-deep .ant-tree > li:last-child {
    padding-bottom: 0;
    // margin-bottom: 7px;
  }
  ::v-deep .ant-tree > li:first-child {
    padding-top: 0;
    // margin-top: 7px;
  }

  // 去掉叶子前的icon
  ::v-deep .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher-noop .anticon-file {
    display: none;
  }
  // 去掉叶子前line
  ::v-deep
    .ant-tree.ant-tree-show-line
    .ant-tree-child-tree
    li:not(.ant-tree-treenode-switcher-open):not(.ant-tree-treenode-switcher-close):has(
      span.ant-tree-switcher.ant-tree-switcher-noop
    )::before {
    display: none;
  }
  ::v-deep
    .ant-tree.ant-tree-show-line
    li:not(.ant-tree-treenode-switcher-open):not(.ant-tree-treenode-switcher-close):has(
      span.ant-tree-switcher.ant-tree-switcher-noop
    )::before {
    display: none;
  }

  // 展开箭头
  ::v-deep .ant-tree.ant-tree-show-line li span.ant-tree-switcher .ant-tree-switcher-icon {
    color: #666;
    font-size: 14px;
  }

  .tree-panel-tree-box {
    position: relative;
    .loading {
      position: absolute;
      width: 100%;
      display: flex;
      justify-content: center;
      top: 30px;
    }
  }
</style>
