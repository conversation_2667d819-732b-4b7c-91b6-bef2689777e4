import request from '@/utils/request'

//班组管理-获取树
export function getObjectCategoryTree() {
  return request({
    url: '/work/group/groupList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 班组人员列表-列表分页查询
export function getObjectCategoryPage(data) {
  return request({
    url: '/work/group/groupUserList',
    method: 'post',
    data,
  })
}

// 水利对象分类-获取父节点树
export function getObjectCategoryParentTree(params) {
  return request({
    url: '/base/objectCategory/getParentTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 水利对象分类-获取排序号
export function getObjectCategoryNextSort(params) {
  return request({
    url: '/base/objectCategory/getNextSort',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 水利对象分类-新增
export function addObjectCategory(data) {
  return request({
    url: '/base/objectCategory/add',
    method: 'post',
    data,
  })
}

// 水利对象分类-更新
export function updateObjectCategory(data) {
  return request({
    url: '/base/objectCategory/update',
    method: 'post',
    data,
  })
}

// 水利对象分类-删除
export function deleteObjectCategory(params) {
  return request({
    url: '/base/objectCategory/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 水利对象分类-详情
export function getObjectCategoryDetail(params) {
  return request({
    url: '/base/objectCategory/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//部门树结构
export function getConfigTree(data) {
  return request({
    url: '/sys/dept/tree',
    method: 'post',
    data,
  })
}

// 班组-新增
export function addPatrolCategory(data) {
  return request({
    url: '/work/group/add',
    method: 'post',
    data,
  })
}

// 班组-更新
export function updatePatrolCategory(data) {
  return request({
    url: '/work/group/update',
    method: 'post',
    data,
  })
}

// 班组——删除
export function deletePatrolCategory(params) {
  return request({
    url: '/work/group/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//人员选择弹框
export function saveRoleUser(data) {
  return request({
    url: '/work/group/chooseUser',
    method: 'post',
    data,
  })
}
//岗位列表查询
export function getPostData() {
  return request({
    url: '/sys/post/list',
    method: 'post',
  })
}
// 班组成员——删除
export function deleteGroupUser(params) {
  return request({
    url: '/work/group/deleteGroupUser',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
