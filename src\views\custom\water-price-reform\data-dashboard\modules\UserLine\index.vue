<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-select
        v-model="queryParam.type"
        placeholder="请选择"
        :options="statisticsType"
        @change="handleChangeType"
      ></a-select>
      <a-range-picker
        v-if="queryParam.type == 3"
        allow-clear
        :placeholder="['开始日期', '结束日期']"
        format="YYYY-MM"
        :value="takeEffect"
        :mode="['month', 'month']"
        @panelChange="handlePanelChange"
      />
      <a-range-picker
        v-else-if="queryParam.type == 5"
        allow-clear
        :value="takeEffect"
        :mode="['year', 'year']"
        format="YYYY"
        formatValue="YYYY"
        :placeholder="['开始日期', '结束日期']"
        @panelChange="handlePanelChange"
      />
      <div v-else-if="queryParam.type == 7" style="display: flex; align-items: center">
        <QuarterPicker v-model="startQuarter" />
        <span style="margin: 0 10px">至</span>
        <QuarterPicker v-model="endQuarter" />
      </div>
    </VxeTableForm>
    <!-- <div class="station-text">水电站调度</div> -->
    <!-- v-if="list?.length" -->
    <hydropowerDispatchBar v-if="list?.length && site.name" :dispatchStatistics="list" :title="site.name" />
    <a-empty v-else style="margin-top: 100px; height: 44%" />
    <div class="export-box">
      <!-- <a-button class="export-btn" type="primary" :loading="exportLoading" icon="download" @click="handleExport">
        导出
      </a-button> -->
    </div>
    <VxeTable
      ref="vxeTableRef"
      :tableTitle="site.name"
      :columns="columns"
      :tableData="tableList"
      :loading="loading"
      :isAdaptPageSize="false"
      @refresh="getList"
      :tablePage="false"
      :footer-row-style="{ background: '#F8F8F9' }"
    ></VxeTable>
  </div>
</template>

<script lang="jsx">
  import { getHydropowerStatistics } from './services'
  import { getDwUseWaters } from '../../services'

  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import hydropowerDispatchBar from '../../components/hydropowerDispatchBar'
  import QuarterPicker from '@/components/QuarterPicker/index.vue'
  import moment from 'moment'

  import * as _ from 'lodash'
  import excelTotalExport from '@/utils/excelTotalExport.js'
  import { region } from 'caniuse-lite'

  export default {
    name: 'hydropowerDispatch',
    components: {
      VxeTableForm,
      VxeTable,
      hydropowerDispatchBar,
      QuarterPicker,
    },
    data() {
      return {
        statisticsType: [
          { label: '按月统计', value: 3 },
          { label: '按季度统计', value: 7 },
          { label: '按年统计', value: 5 },
        ],

        list: [],
        tableList: [],
        footerData: [{ seq: '合计', num: '282', num: '282' }],

        startQuarter: [
          moment().startOf('year').startOf('quarter').format('YYYY-MM-DD'),
          moment().startOf('year').endOf('quarter').format('YYYY-MM-DD'),
        ],
        endQuarter: [
          moment().startOf('quarter').format('YYYY-MM-DD'),
          moment().endOf('quarter').endOf('month').format('YYYY-MM-DD'),
        ],

        loading: false,
        exportLoading: false,
        total: 0,
        takeEffect: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')],
        queryParam: {
          endTime: moment().endOf('month').format('YYYY-MM-DD'),
          startTime: moment().startOf('year').format('YYYY-MM-DD'),
          type: 3,
          useWaterId: 1,
        },
        columnsDefault: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '用水户编码',
            field: 'useWaterCode',
            minWidth: 140,
            fixed: 'left',
            showOverflow: 'tooltip',
          },
          {
            title: '用水户名称',
            field: 'useWaterName',
            minWidth: 90,
            fixed: 'left',
          },
          {
            title: '行政区划',
            field: 'districtName',
            minWidth: 80,
            fixed: 'left',
            showOverflow: 'tooltip',
          },
          {
            title: '时段内总用水量(万m³)',
            field: 'totalUsage',
            minWidth: 168,
            fixed: 'left',
          },
        ],
        columns: [],
      }
    },
    props: {
      site: {
        type: Object,
        default: () => ({ key: 12, districtName: '测试站点' }),
      },
    },
    watch: {
      site(val) {
        this.getList()
      },
    },
    mounted() {
      this.getList()
    },
    methods: {
      /** 查询列表 */
      getList() {
        this.loading = true
        console.log('***** 获取时间 yue', this.queryParam.startTime, this.queryParam.endTime)
        const params = {
          ...this.queryParam,
          useWaterId: this.site.key,
          startTime:
            this.queryParam.type === 7 ? moment(this.startQuarter[0]).format('YYYY-MM-DD') : this.queryParam.startTime,
          endTime:
            this.queryParam.type === 7
              ? moment(this.endQuarter[1]).endOf('month').format('YYYY-MM-DD')
              : this.queryParam.endTime,
        }
        getDwUseWaters(params).then(response => {
          this.loading = false
          let data = response?.data

          // let list = response?.data?.userWaters
          this.list = data.length > 1 ? data[0]?.userWaters : data?.userWaters
          let list = data.length > 1 ? data[0]?.userWaters : data?.userWaters
          this.tableList = this.transformMultipleUsersToRows(data)
          setTimeout(() => {
            this.columns = this.generateDynamicColumns(this.columnsDefault, list)
          }, 300)
        })
      },

      transformMultipleUsersToRows(users) {
        let list = [users]
        return list?.map(user => {
          let datesArray = user?.userWaters
          let totalUsage = user?.userWaters.reduce((sum, item) => {
            // 如果 value 是数字，则加到总和中，否则加 0
            return sum + (typeof item.value === 'number' ? item.value : 0)
          }, 0)
          let row = { ...user, totalUsage: totalUsage }
          datesArray?.forEach(date => {
            row[`${date.dateTime}`] = date.value
          })

          return row
        })
      },
      generateDynamicColumns(originalColumns, dates) {
        // 创建新的列数组，先复制原始固定列
        const newColumns = [...originalColumns]
        // 遍历dates数组，为每个日期创建列
        dates?.forEach(dateObj => {
          const month = dateObj.dateTime // 获取年月，如 "2025-01"

          // 添加动态时间列
          newColumns.push({
            title: month,
            field: `${month}`, // 使用唯一字段名
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                // 找到对应的日期数据
                const dateData = dates?.find(d => d.dateTime == month)
                // 返回该日期的sumDispatchFlow值
                // return dateData ? dateData.value : '-'
                return dateData.value
              },
            },
          })
        })
        return newColumns
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.takeEffect = [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')]
        this.queryParam = {
          endTime: moment().endOf('month').format('YYYY-MM-DD'),
          startTime: moment().startOf('year').format('YYYY-MM-DD'),
          type: 3,
        }
        this.handleQuery()
      },
      handlePanelChange(value, mode) {
        // console.log('***** 切换时间', value, mode)
        this.takeEffect = value
        // if (value.length == 0) {
        //   return
        // }
        if (this.queryParam.type == 3) {
          this.queryParam.startTime = value[0] ? moment(value[0]).format('YYYY-MM-DD') : null
          this.queryParam.endTime = value[1] ? moment(value[1]).endOf('month').format('YYYY-MM-DD') : null
          this.getList()
        } else if (this.queryParam.type == 5) {
          this.queryParam.startTime = value[0] ? moment(value[0]).format('YYYY') : null
          this.queryParam.endTime = value[1] ? moment(value[1]).format('YYYY') : null
          this.getList()
        }
      },
      handleChangeType(val) {
        if (val == 3) {
          this.resetQuery()
        } else if (val == 5) {
          this.takeEffect = [moment().format('YYYY'), moment().format('YYYY')]
          this.queryParam.startTime = moment().format('YYYY')
          this.queryParam.endTime = moment().format('YYYY')
          this.handleQuery()
        } else if (val == 7) {
          this.startQuarter = [
            moment().startOf('year').startOf('quarter').format('YYYY-MM-DD'),
            moment().startOf('year').endOf('quarter').format('YYYY-MM-DD'),
          ]
          this.endQuarter = [
            moment().startOf('quarter').format('YYYY-MM-DD'),
            moment().endOf('quarter').endOf('month').format('YYYY-MM-DD'),
          ]
          this.queryParam.startTime = this.startQuarter[0]
          this.queryParam.endTime = this.endQuarter[1]
          this.handleQuery()
        }
      },
      getSummation(key) {
        return this.list.map(el => el[key]).reduce((a, b) => a + b, 0)
      },
      // 导出
      handleExport() {
        this.exportLoading = true
        getHydropowerStatistics(this.queryParam).then(res => {
          this.exportLoading = false

          const columnsList = [
            {
              title: '序号',
              field: 'seq',
              minWidth: 80,
            },
            {
              title: '统计月份',
              field: 'dateTime',
              minWidth: 80,
            },
            {
              title: '月度总发电量(kw·h)',
              field: 'sumFlow',
              minWidth: 110,
            },
            // {
            //   title: '日常调度发电量(kw·h)',
            //   field: 'sumDispatchFlow',
            //   minWidth: 120,
            // },
            // {
            //   title: '临时调度次数(次)',
            //   field: 'extraCount',
            //   minWidth: 100,
            // },
            // {
            //   title: '临时调度发电量(kw·h)',
            //   field: 'sumExtraFlow',
            //   minWidth: 120,
            // },
          ]

          const data = (res.data || []).map((el, i) => ({
            ...el,
            seq: i + 1,
          }))
          const total = []
          // const total = [
          //   '合计',
          //   '',
          //   this.getSummation('sumFlow').toFixed(2),
          //   this.getSummation('sumDispatchFlow').toFixed(2),
          //   this.getSummation('extraCount').toFixed(0),
          //   this.getSummation('sumExtraFlow').toFixed(2),
          // ]

          excelTotalExport(columnsList, data, total, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`)
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .common-table-page {
    background: #fff;

    .vxe-table-form {
      height: 70px;
    }

    .station-text {
      padding-left: 20px;
    }

    .vxe-table-content {
      height: 200px;
    }

    .export-box {
      display: flex;

      .export-btn {
        margin-left: auto;
        margin-right: 20px;
      }
    }
  }

  .chart {
    width: 100%;
    height: 300px;
  }
</style>
