<template>
  <a-form-model
    style="margin-top: 24px"
    ref="form"
    :model="form"
    :rules="rules"
    layout="horizontal"
    v-bind="{
      labelCol: { span: 6 },
      wrapperCol: { span: 8 },
    }"
  >
    <a-form-model-item label="方案名称" prop="caseName">
      <a-input v-model="form.caseName" placeholder="请输入" allow-clear />
    </a-form-model-item>
    <a-form-model-item label="仿真类型" prop="simulateType">
      <a-radio-group
        v-model="form.simulateType"
        :options="simulateTypeOptions"
        style="margin-top: 5px"
        @change="changeSimulateType"
      />
    </a-form-model-item>
    <a-form-model-item label="仿真时段" v-if="form.simulateType === 1">
      <a-range-picker
        :disabled="form.simulateType === 1"
        v-model="form.range"
        :placeholder="['开始时间', '结束时间']"
        format="YYYY-MM-DD HH:00"
        :show-time="{ format: 'HH' }"
      />
    </a-form-model-item>
    <a-form-model-item label="仿真时段" prop="range" v-if="form.simulateType === 2">
      <a-range-picker
        v-model="form.range"
        :placeholder="['开始时间', '结束时间']"
        format="YYYY-MM-DD HH:00"
        :show-time="{ format: 'HH' }"
      />
      <a-tooltip>
        <template slot="title">可对未来72h的数据进行预报模拟</template>
        ⓘ
      </a-tooltip>
    </a-form-model-item>

    <!-- <a-form-model-item label="仿真类型" prop="simulateType">
      <a-radio-group
        v-model="form.simulateType"
        :options="simulateTypeOptions"
        style="margin-top: 5px"
        @change="changeSimulateType"
      />
    </a-form-model-item>
    <a-form-model-item label="调度方案名称" prop="resvrDispId" v-if="form.simulateType === 1">
      <a-select
        v-model="form.resvrDispId"
        placeholder="请输入"
        :options="dispatchCaseOptions"
        @change="changeDispatchCase"
      />
    </a-form-model-item>
    <a-form-model-item label="仿真时段" prop="range">
      <a-range-picker
        :disabled="form.simulateType === 1"
        v-model="form.range"
        :placeholder="['开始时间', '结束时间']"
        format="YYYY-MM-DD HH:00"
        :show-time="{ format: 'HH' }"
      />
    </a-form-model-item>
    <a-form-model-item label="调度类型" prop="dispathType">
      <a-radio-group
        :disabled="form.simulateType === 1"
        v-model="form.dispathType"
        :options="dispatchTypeOptions"
        style="margin-top: 5px"
      />
    </a-form-model-item>

    <a-form-model-item label="仿真范围" prop="fcstRange">
      <a-radio-group v-model="form.fcstRange" :options="fcstRangeOptions" style="margin-top: 5px" />
    </a-form-model-item> -->
  </a-form-model>
</template>

<script>
  import moment from 'moment'
  import { getResvrDispPage } from '../../../dispatch-model/services'
  // import { dispatchTypeOptions, simulateTypeOptions } from '../../config'
  import { dispatchTypeOptions } from '../../config'

  export default {
    name: 'BasicInfo',
    props: ['inWaterEchoData', 'fcstRangeOptions'],
    data() {
      return {
        dispatchCaseOptions: [],
        dispatchTypeOptions,
        simulateTypeOptions: [
          { label: '历史调度', value: 1 },
          { label: '未来预报', value: 2 },
        ],
        form: {
          caseName: undefined,
          simulateType: 2,
          range: undefined,
          resvrDispId: undefined,
          dispathType: undefined,
          fcstRange: this.fcstRangeOptions[0].value,
        },
        rules: {
          caseName: [{ required: true, message: '方案名称不能为空', trigger: 'blur' }],
          simulateType: [{ required: true, message: '仿真类型不能为空', trigger: 'change' }],
          range: [{ required: true, message: '仿真时段不能为空', trigger: 'change' }],
          // resvrDispId: [{ required: true, message: '调度方案不能为空', trigger: 'change' }],
          // dispathType: [{ required: true, message: '调度类型不能为空', trigger: 'change' }],
          // fcstRange: [{ required: true, message: '预报范围不能为空', trigger: 'change' }],
        },
      }
    },
    computed: {},
    watch: {
      inWaterEchoData: {
        handler(val) {
          if (!!val) {
            this.form = {
              caseName: undefined,
              simulateType: this.inWaterEchoData.simulateType,
              range: [moment(this.inWaterEchoData.startTime), moment(this.inWaterEchoData.endTime)],
              resvrDispId: this.inWaterEchoData.resvrDispId,
              dispathType: this.inWaterEchoData.dispathType,
              fcstRange: this.inWaterEchoData.fcstRange,
            }
          }
        },
      },
      'form.simulateType'(val) {
        if (val == '2') {
          this.form.range = [
            moment().add(1, 'days').format('YYYY-MM-DD HH:00'),
            moment().add(3, 'days').format('YYYY-MM-DD HH:00'),
          ]
        }
      },
    },
    created() {
      getResvrDispPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
        this.dispatchCaseOptions = (res.data?.data || []).map(el => ({
          ...el,
          label: el.caseName,
          value: el.resvrDispId,
        }))
      })
      this.form.range = [
        moment().add(1, 'days').format('YYYY-MM-DD HH:00'),
        moment().add(3, 'days').format('YYYY-MM-DD HH:00'),
      ]
    },
    methods: {
      changeSimulateType() {
        this.form.resvrDispId = undefined
        this.form.range = undefined
        this.form.dispathType = undefined
      },
      changeDispatchCase(val) {
        const obj = this.dispatchCaseOptions.find(el => el.value === val)
        this.form.range = [moment(obj.startTime), moment(obj.endTime)]
        this.form.dispathType = obj.dispathMode
      },

      save() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.$emit('saveData', {
              ...this.form,
              startTime: moment(this.form.range[0]).format('YYYY-MM-DD HH:00'),
              endTime: moment(this.form.range[1]).format('YYYY-MM-DD HH:00'),
            })
          } else {
            this.$emit('saveData', false)
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped></style>
