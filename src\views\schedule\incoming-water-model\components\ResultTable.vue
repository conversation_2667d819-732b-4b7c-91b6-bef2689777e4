<template>
  <VxeTable
    ref="vxeTableRef"
    :columns="columns"
    :tableData="$attrs.dataSource"
    tableTitle="预报结果"
    size="small"
    :tablePage="false"
    :isShowSetBtn="false"
    :isShowTableHeader="isShowTableHeader"
  ></VxeTable>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable/index.vue'

  export default {
    name: 'ResultTable',

    components: {
      VxeTable,
    },
    props: {
      isShowTableHeader: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        columns: [
          {
            title: '时间',
            field: 'tm',
            minWidth: 145,
          },
          {
            field: 'rain',
            minWidth: 90,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>时段雨量</div>
                  <span>(mm)</span>
                </div>
              ),
            },
          },
          {
            field: 'sumRain',
            minWidth: 90,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>累计降雨量</div>
                  <span>(mm)</span>
                </div>
              ),
            },
          },
          {
            field: 'inflow',
            minWidth: 100,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>来水流量</div>
                  <span>(m³/s)</span>
                </div>
              ),
            },
          },
          {
            field: 'outflow',
            minWidth: 100,
            align: 'center',
            slots: {
              header: () => (
                <div>
                  <div>出库流量</div>
                  <span>(m³/s)</span>
                </div>
              ),
            },
          },
          // {
          //   field: 'wlv',
          //   minWidth: 70,
          //   align: 'center',
          //   slots: {
          //     header: () => (
          //       <div>
          //         <div>水位</div>
          //         <span>(m)</span>
          //       </div>
          //     ),
          //   },
          // },
        ],
      }
    },
    computed: {},
    created() {},
    methods: {},
  }
</script>

<style lang="less" scoped></style>
