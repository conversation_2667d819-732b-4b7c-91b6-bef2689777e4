<template>
  <div class="container">
    <a-form-model
      style="margin-top: 24px"
      ref="form"
      :model="form"
      :rules="rules"
      layout="horizontal"
      v-bind="{
        labelCol: { span: 6 },
        wrapperCol: { span: 8 },
      }"
    >
      <a-form-model-item label="方案名称" prop="caseName">
        <a-input v-model="form.caseName" placeholder="请输入" allow-clear />
      </a-form-model-item>
      <a-form-model-item label="模型应用场景" prop="scene">
        <a-radio-group
          v-model="form.scene"
          :options="sceneOptions.map(el => ({ ...el, label: `${el.label}场景` }))"
          style="margin-top: 5px"
          @change="changeScene"
        />
      </a-form-model-item>
      <a-form-model-item label="开始时间" prop="startTime">
        <a-date-picker
          v-model="form.startTime"
          :disabled="form.scene === 2"
          format="YYYY-MM-DD HH:00"
          valueFormat="YYYY-MM-DD HH:00"
          :show-time="{ format: 'HH' }"
          :disabled-date="disabledStartDate"
        />
      </a-form-model-item>
      <a-form-model-item label="结束时间" prop="endTime">
        <a-date-picker
          v-model="form.endTime"
          format="YYYY-MM-DD HH:00"
          valueFormat="YYYY-MM-DD HH:00"
          :show-time="{ format: 'HH' }"
          :disabled-date="disabledEndDate"
        />
      </a-form-model-item>
      <!-- <a-form-model-item label="预报范围" prop="fcstRange">
        <a-radio-group v-model="form.fcstRange" :options="fcstRangeOptions" style="margin-top: 5px" />
      </a-form-model-item> -->
    </a-form-model>
  </div>
</template>

<script>
  import moment from 'moment'
  export default {
    name: 'BasicInfo',
    props: ['fcstRangeOptions', 'sceneOptions'],
    components: {},
    data() {
      return {
        form: {
          caseName: undefined,
          scene: 2, // 默认选中未来预报场景
          fcstRange: this.fcstRangeOptions[0].value,
          startTime: undefined,
          endTime: undefined,
        },
        rules: {
          caseName: [{ required: true, message: '方案名称不能为空', trigger: 'blur' }],
          scene: [{ required: true, message: '模型应用场景不能为空', trigger: 'change' }],
          fcstRange: [{ required: true, message: '预报范围不能为空', trigger: 'change' }],
          startTime: [{ required: true, message: '开始时间不能为空', trigger: 'change' }],
          endTime: [{ required: true, message: '结束时间不能为空', trigger: 'change' }],
        },
      }
    },
    computed: {},
    created() {
      // 由于默认选中未来预报场景，需要初始化时间
      if (this.form.scene === 2) {
        this.form.startTime = moment().add(1, 'hours').format('YYYY-MM-DD HH:00')
        this.form.endTime = moment().add(1, 'hours').add(3, 'days').format('YYYY-MM-DD HH:00')
      }
    },
    methods: {
      changeScene(val) {
        if (val.target.value === 2) {
          this.form.startTime = moment().add(1, 'hours').format('YYYY-MM-DD HH:00')
          this.form.endTime = moment().add(1, 'hours').add(3, 'days').format('YYYY-MM-DD HH:00')
          this.$refs.form.validateField('startTime')
          this.$refs.form.validateField('endTime')
        } else {
          this.form.startTime = undefined
          this.form.endTime = undefined
        }
      },
      disabledStartDate(current) {
        if (this.form.scene === 1) {
          if (this.form.endTime) {
            return (
              current < moment(this.form.endTime).subtract(15, 'days') ||
              current > moment(this.form.endTime) ||
              current > moment()
            )
          }
          return current && current > moment()
        } else {
          // 未来预报场景：开始时间虽然被禁用，但逻辑上应该可选择今天及以后
          return current < moment().startOf('day')
        }
      },
      disabledEndDate(current) {
        if (this.form.scene === 1) {
          if (this.form.startTime) {
            return (
              current < moment(this.form.startTime) ||
              current > moment(this.form.startTime).add(15, 'days') ||
              current > moment()
            )
          }
          return current && current > moment()
        } else {
          // 未来预报场景：可选择今天到今天+3天
          return current < moment().startOf('day') || current > moment().add(3, 'days').endOf('day')
        }
      },

      save() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.$emit('saveData', this.form)
          } else {
            this.$emit('saveData', false)
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped></style>
