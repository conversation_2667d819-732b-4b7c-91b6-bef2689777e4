<template>
  <div class="result-container">
    <div v-if="loading" class="loading-container">
      <a-spin></a-spin>
    </div>

    <div v-else-if="!!errorInfo" class="error-container">
      <a-result status="error" :sub-title="errorInfo"></a-result>
    </div>

    <div v-else-if="!!resultData" class="result-content">
      <!-- 标题 -->
      <div class="result-header">
        <h3>预报结果</h3>
      </div>
      
      <!-- 内容区域：左边图表，右边表格 -->
      <div class="result-body">
        <!-- 左侧图表 -->
        <div class="chart-panel">
          <BarAndLineMix
            :dataSource="chartData"
            :markLineXAxis="resultData?.reals?.length - 1"
            :markPointsData="markPointsData"
          />
        </div>
        
        <!-- 右侧表格 -->
        <div class="table-panel">
          <VxeTable
            ref="resultTableRef"
            :isShowTableHeader="false"
            :isShowSetBtn="false"
            :columns="tableColumns"
            :tableData="tableData"
            :tablePage="false"
            height="100%"
          ></VxeTable>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import { forecast, getInWater } from '../../../services'
  import { SocketClient } from '@/utils/sockClient.js'
  import BarAndLineMix from '../../../components/BarAndLineMix.vue'
  import VxeTable from '@/components/VxeTable/index.vue'
  import { maxBy } from 'lodash'

  export default {
    name: 'Result',
    props: ['baseInfo', 'rainfall'],
    components: { BarAndLineMix, VxeTable },
    data() {
      return {
        loading: false,
        socketIns: null,
        errorInfo: null,
        resultData: null,
        chartData: [],
        tableColumns: [
          { title: '时间', field: 'tm', minWidth: 150 },
          { title: '时段雨量(mm)', field: 'rain', minWidth: 120, align: 'center' },
          { title: '累计降雨量(mm)', field: 'sumRain', minWidth: 120, align: 'center' },
          { title: '预报来水流量(m³/s)', field: 'inflow', minWidth: 140, align: 'center' },
          { title: '累计来水量(万m³)', field: 'sumInflow', minWidth: 130, align: 'center' }
        ]
      }
    },
    computed: {
      markPointsData() {
        if (!this.resultData || !this.resultData.fcsts) return { water: [], flow: [] }
        
        const predFlow = maxBy(this.resultData.fcsts, 'wlv')
        const predFlowMax = {
          x:
            this.resultData.fcsts.findIndex(el => (predFlow ? predFlow.tm === el.tm : false)) +
            this.resultData.reals.length,
          y: predFlow?.inflow,
        }

        const predWater = maxBy(this.resultData.fcsts, 'wlv')

        const predWaterMax = {
          x:
            this.resultData.fcsts.findIndex(el => (predWater ? predWater.tm === el.tm : false)) +
            this.resultData.reals.length,
          y: predWater?.wlv,
        }

        return {
          water: [{ name: '预测', value: predWaterMax.y, xAxis: predWaterMax.x, yAxis: predWaterMax.y }],
          flow: [{ value: predFlowMax.y, xAxis: predFlowMax.x, yAxis: predFlowMax.y }],
        }
      },
      
      tableData() {
        if (!this.resultData) return []
        
        const allData = [...(this.resultData.reals || []), ...(this.resultData.fcsts || [])]
        let sumRain = 0
        let sumInflow = 0
        
        return allData.map(item => {
          sumRain += item.rain || 0
          sumInflow += (item.inflow || 0) * 3.6 / 10000 // 转换为万m³，假设时段为1小时
          
          return {
            tm: item.tm,
            rain: item.rain ? +item.rain.toFixed(1) : 0,
            sumRain: +sumRain.toFixed(1),
            inflow: item.inflow ? +item.inflow.toFixed(1) : 0,
            sumInflow: +sumInflow.toFixed(2)
          }
        })
      }
    },
    watch: {
      resultData: {
        handler(newVal) {
          if (!newVal) return
          
          const data = newVal
          const rainData = {
            name: '时段雨量',
            data: data.reals.map(el => [el.tm, el.rain]).concat(data.fcsts.map(el => [el.tm, el.rain])),
          }
          const sumRainData = {
            name: '累计降雨量',
            data: rainData.data.map((el, idx) => {
              const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0)
              return [el[0], +sum.toFixed(1)]
            }),
          }

          this.chartData = [
            rainData,
            sumRainData,
            {
              name: '来水流量',
              data: data.reals.map(el => [el.tm, el.inflow]).concat(data.fcsts.map(el => [el.tm, el.inflow])),
            },
          ]
        },
        deep: true,
        immediate: true,
      },
    },
    created() {
      this.loading = true
      this.$emit('update:isDisabledBtn',  true)
      this.errorInfo = null

      // 注释接口请求，使用测试数据
      /*
      forecast({ ...this.baseInfo, rains: this.rainfall }).then(res => {
        getInWater({ inWaterId: res.data })
          .then(resp => {
            this.resultData = resp.data
          })
          .finally(() => {
            this.$emit('update:isDisabledBtn', false)
            this.loading = false
          })
        return

        this.socketIns = new SocketClient()

        this.socketIns.connect('/topic/model/result', response => {
          if (response.code == 200) {
            if (res.data == response.data.id && response.data.modelType === 'LSYB') {
              // 发请求
              getInWater({ inWaterId: response.data.id })
                .then(resp => {
                  this.resultData = resp.data
                })
                .finally(() => {
                  this.$emit('update:isDisabledBtn', false)
                  this.loading = false
                })
            }
          } else {
            this.$emit('update:isDisabledBtn', false)
            this.loading = false
            this.errorInfo = response.message || '模型计算异常了'
          }
        })
      })
      */

      // 使用测试数据
      this.generateTestResultData()
    },
    beforeDestroy() {
      if (this.socketIns) {
        this.socketIns.disconnect()
      }
    },
    methods: {
      save() {
        this.$emit('saveData', this.resultData)
      },

      // 生成测试结果数据
      generateTestResultData() {
        // 模拟延迟
        setTimeout(() => {
          const baseTime = new Date()
          
          // 生成历史数据（reals）- 过去24小时
          const reals = []
          let cumulativeRain = 0
          for (let i = 23; i >= 0; i--) {
            const time = new Date(baseTime.getTime() - i * 60 * 60 * 1000)
            const timeStr = time.toISOString().slice(0, 16).replace('T', ' ')
            
            // 模拟一个降雨过程：前期无雨，中期有雨，后期减少
            let rain = 0
            if (i <= 16 && i >= 8) {
              rain = +(Math.random() * 8 + 2).toFixed(1) // 2-10mm
            } else if (i <= 7 && i >= 4) {
              rain = +(Math.random() * 15 + 5).toFixed(1) // 5-20mm
            } else if (i <= 3) {
              rain = +(Math.random() * 5).toFixed(1) // 0-5mm
            }
            
            cumulativeRain += rain
            
            // 流量跟随降雨有一定的延迟和放大效应
            const baseFlow = 120
            const rainEffect = cumulativeRain * 2 + rain * 5
            const inflow = +(baseFlow + rainEffect + Math.random() * 50).toFixed(1)
            
            reals.push({
              tm: timeStr,
              rain: rain,
              inflow: inflow,
              wlv: +(50 + Math.random() * 8 + cumulativeRain * 0.1).toFixed(2)
            })
          }
          
          // 生成预测数据（fcsts）- 未来24小时
          const fcsts = []
          for (let i = 1; i <= 24; i++) {
            const time = new Date(baseTime.getTime() + i * 60 * 60 * 1000)
            const timeStr = time.toISOString().slice(0, 16).replace('T', ' ')
            
            // 预测降雨模式：前12小时强降雨，后12小时递减
            let rain = 0
            if (i <= 6) {
              rain = +(Math.random() * 25 + 10).toFixed(1) // 10-35mm强降雨
            } else if (i <= 12) {
              rain = +(Math.random() * 15 + 5).toFixed(1) // 5-20mm中等降雨
            } else if (i <= 18) {
              rain = +(Math.random() * 8 + 2).toFixed(1) // 2-10mm小雨
            } else {
              rain = +(Math.random() * 3).toFixed(1) // 0-3mm微雨
            }
            
            cumulativeRain += rain
            
            // 预测流量会随着降雨增加，并有峰值出现
            const baseFlow = 150
            const peakFactor = i <= 8 ? (8 - Math.abs(i - 4)) * 0.3 : 0 // 在第4小时达到峰值
            const rainEffect = rain * 8 + peakFactor * 100
            const inflow = +(baseFlow + rainEffect + Math.random() * 80).toFixed(1)
            
            fcsts.push({
              tm: timeStr,
              rain: rain,
              inflow: inflow,
              wlv: +(52 + Math.random() * 6 + cumulativeRain * 0.08).toFixed(2)
            })
          }
          
          // 模拟结果数据结构
          this.resultData = {
            reals: reals,
            fcsts: fcsts,
            id: 'test_forecast_' + Date.now(),
            createTime: baseTime.toISOString(),
            status: 'completed'
          }
          
          this.$emit('update:isDisabledBtn', false)
          this.loading = false
        }, 1500) // 模拟1.5秒的计算时间
      },
    },
  }
</script>

<style lang="less" scoped>
  .result-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .loading-container,
  .error-container {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .result-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .result-header {
    padding: 16px 0 8px 0;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }

  .result-body {
    flex: 1;
    display: flex;
    gap: 20px;
    min-height: 0;
  }

  .chart-panel {
    flex: 1.5;
    min-height: 400px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 16px;
  }

  .table-panel {
    flex: 1;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 16px;
    overflow: hidden;
  }
</style>
