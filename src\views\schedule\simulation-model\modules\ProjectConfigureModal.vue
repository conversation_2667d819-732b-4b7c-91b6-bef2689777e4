<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="900"
    @cancel="cancel"
    modalHeight="800"
  >
    <div slot="content">
      <a-tabs v-model="activeFcstRange" size="small">
        <a-tab-pane v-for="(el, idx) in list" :key="el.parentId" :tab="el.parentName">
          <div style="text-align: right; padding-bottom: 10px">
            <a-button size="small" @click="handleAdd">增加</a-button>
          </div>

          <a-table :columns="columns" :data-source="el.projects" :pagination="false" rowKey="rowKey" />
        </a-tab-pane>
      </a-tabs>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="onSubmit" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import { getChSimRange, setChSimRange, getProjectByCategoryId } from '../services'

  export default {
    name: 'DitchConfigureModal',
    props: ['fcstRangeOptions'],
    components: { AntModal },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '工程配置',
        activeFcstRange: undefined,
        projectTypeOptions: [],
        projectOptions: [],
        list: [],
        columns: [
          {
            title: '工程名称',
            customRender: (text, record, index) => {
              const idx = this.list.findIndex(el => el.parentId === this.activeFcstRange)
              const cIndexs = this.list[idx].projects.map(el => el.projectId)
              const cOptions = this.projectOptions.filter(
                el => !cIndexs.includes(el.value) || el.value === record.projectId,
              )

              if (this.list[idx].projects[index] === undefined) return

              const obj = {
                attrs: { colSpan: 3 },
                children: (
                  <a-select
                    style={{ width: '100%' }}
                    v-model={this.list[idx].projects[index].projectId}
                    placeholder='请选择'
                    show-search
                    option-filter-prop='children'
                    filter-option={(input, option) => {
                      return (
                        option.componentOptions.children[0].children[0].children[0].text
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      )
                    }}
                  >
                    {cOptions.map((el, k) => {
                      return (
                        <a-select-option key={k} value={el.value}>
                          <div class='terminal-select-item'>
                            <span>{el.projectName}</span>
                            <span>{el.projectCode}</span>
                            <span>{el.objectCategoryName}</span>
                          </div>
                        </a-select-option>
                      )
                    })}
                  </a-select>
                ),
              }
              return obj
            },
          },
          {
            title: '工程编码',
            customRender: (v, r, i) => {
              const obj = {
                attrs: { colSpan: 0 },
              }
              return obj
            },
          },
          {
            title: '工程类型',
            width: 110,
            customRender: (v, r, i) => {
              const obj = {
                attrs: { colSpan: 0 },
              }
              return obj
            },
          },
          {
            title: '操作',
            align: 'center',
            customRender: (text, record, index) => {
              return (
                <a
                  onClick={() => {
                    const idx = this.list.findIndex(el => el.parentId === this.activeFcstRange)
                    this.list[idx].projects = this.list[idx].projects
                      .filter(el => {
                        return el.projectId !== record.projectId
                      })
                      .map((el, idx) => ({ ...el, rowKey: idx }))
                  }}
                >
                  删除
                </a>
              )
            },
          },
        ],
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      handleShow() {
        this.open = true
        this.modalLoading = true

        getOptions('modelCategoryCode').then(res => {
          this.projectTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
          console.log('***** 152 projectTypeOptions', this.projectTypeOptions, res)
          Promise.all(this.projectTypeOptions.map(el => getProjectByCategoryId({ objectCategoryCode: el.value }))).then(
            res => {
              const arr = []
              res.forEach((el, idx) => {
                arr.push(
                  ...el.data.map(ele => ({
                    ...ele,
                    label: ele.projectName,
                    value: ele.projectId,
                    objectCategoryName: this.projectTypeOptions[idx]?.label,
                  })),
                )
              })
              this.projectOptions = arr

              getChSimRange({ type: 2 }).then(resp => {
                this.activeFcstRange = resp.data[0]?.parentId

                this.list = resp.data.projects.map((el, idx) => {
                  return {
                    parentId: el.parentId,
                    parentName: el.parentName,
                    projects: resp.data[idx].projects.map((elem, index) => ({
                      ...elem,
                      objectCategoryName: this.projectTypeOptions?.find(item => item.value === elem.objectCategoryCode)
                        ?.label,
                      rowKey: index,
                    })),
                  }
                })
                console.log('***** 182 list', this.list, this.activeFcstRange, resp)
                this.modalLoading = false
              })
            },
          )
        })
      },

      handleAdd() {
        const idx = this.list.findIndex(el => el.parentId === this.activeFcstRange)

        this.list[idx].projects.push({
          rowKey: this.list[idx].projects.length,
          projectId: undefined,
          projectName: undefined,
        })
      },

      onSubmit() {
        setChSimRange(this.list.map(el => ({ ...el, type: 2, projectId: el.projects.map(ele => ele.projectId) }))).then(
          res => {
            this.$message.success('配置成功', 3)
            this.$emit('close')
            this.$emit('ok')
          },
        )
      },
    },
  }
</script>

<style lang="less" scoped>
  .terminal-select-item {
    display: flex;
    width: 100%;

    > span {
      text-align: left;
      width: calc(50% - 50px);
      &:last-child {
        width: 100px;
        text-align: center;
      }
    }
  }

  ::v-deep .ant-select-selection-selected-value {
    width: 100%;
  }

  ::v-deep .ant-table-wrapper {
    margin: 0;
  }
</style>
